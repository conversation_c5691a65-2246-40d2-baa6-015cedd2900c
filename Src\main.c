/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "dma.h"
#include "lwip.h"
#include "rtc.h"
#include "spi.h"
#include "tim.h"
#include "usart.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "xsj_lib.h"
#include "uart.h"
#include "ad7792.h"
#include <stdio.h>
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */

extern void stm_gpio_IOconfig(void);
extern void umain(void);
void task_Fun(void);
void task_AD(void);
void task_500us(void);
void bcode_recv(unsigned short PW_time);

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

//B码
uint8_t g_Bcode_state =0;       //1:上升沿中断  2：下降沿中断
uint8_t g_Bcode_flg =0;         //可读取标志
uint32_t g_Bcode_temp_time =0;
uint32_t g_Bcode_time =0;       //高电平持续时间

//定时器
static uint32_t g_tim5_100us =0;

//频率采集
#define htim_adc        htim1
#define TIM_ICOF_MAX    0x32
uint32_t capture_Buf[6]={0};   //捕获值
uint8_t capture_Cnt=0;         //设置状态标志位
uint8_t overload_Cnt=0;        //溢出标志
uint8_t temp_cnt = 0;
uint8_t capture_Cnt1=0;        //设置状态标志位
uint8_t overload_Cnt1=0;       //溢出标志
int32_t high_time;             //高电平时间
int32_t low_time;              //低电平时间
int32_t high_time1;            //高电平时间
int32_t low_time1;             //低电平时间
double HL_time;
double LL_time;
double HL_time1;
double LL_time1;
double fre;                   // 频率 FM2
double duty;
double fre1;                  // 频率 FM1
double duty1; 
double g_FM1 =0;
double g_FM2 = 0;

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */
	__enable_irq();
  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_DMA_Init();
  MX_TIM2_Init();
  MX_RTC_Init();
  MX_SPI1_Init();
  MX_SPI2_Init();
  MX_SPI3_Init();
  MX_TIM4_Init();
  MX_TIM5_Init();
  MX_UART7_Init();
  MX_USART3_UART_Init();
  MX_USART6_UART_Init();
  MX_LWIP_Init();
  MX_TIM1_Init();
  MX_USART2_UART_Init();
  /* USER CODE BEGIN 2 */

    //串口0
	__HAL_UART_ENABLE_IT(&huart2, UART_IT_IDLE);//使能串口空闲中断
	HAL_UART_Receive_DMA(&huart2, g_rx0_buffer, COM_RX_BUFFER_SIZE);//启动串口DMA接收

    //串口1
	__HAL_UART_ENABLE_IT(&huart7, UART_IT_IDLE);//使能串口空闲中断
	HAL_UART_Receive_DMA(&huart7, g_rx1_buffer, COM_RX_BUFFER_SIZE);//启动串口DMA接收

    //串口2
	__HAL_UART_ENABLE_IT(&huart3, UART_IT_IDLE);//使能串口空闲中断
	HAL_UART_Receive_DMA(&huart3, g_rx2_buffer, COM_RX_BUFFER_SIZE);//启动串口DMA接收

    //串口励磁
	__HAL_UART_ENABLE_IT(&huart6, UART_IT_IDLE);//使能串口空闲中断
	HAL_UART_Receive_DMA(&huart6, g_rx3_buffer, COM_RX_BUFFER_SIZE);//启动串口DMA接收

    //频率采集 - TIM1
    __HAL_TIM_SET_CAPTUREPOLARITY(&htim_adc, TIM_CHANNEL_2, TIM_INPUTCHANNELPOLARITY_RISING);  //设置为上升沿捕获
    __HAL_TIM_SET_CAPTUREPOLARITY(&htim_adc, TIM_CHANNEL_3, TIM_INPUTCHANNELPOLARITY_RISING);  //设置为上升沿捕获
    HAL_TIM_IC_Start_IT(&htim_adc,	 TIM_CHANNEL_2);  //开启输入中断
    HAL_TIM_IC_Start_IT(&htim_adc,	 TIM_CHANNEL_3);  //开启输入中断
    __HAL_TIM_ENABLE_IT(&htim_adc,  TIM_IT_UPDATE);  //更新中断用于溢出计数

    //SPI1 SPI2 SPI3
    HAL_SPI_MspInit(&hspi1);
    HAL_SPI_MspInit(&hspi2);
    HAL_SPI_MspInit(&hspi3);

    //通用定时器
    HAL_TIM_Base_Start_IT(&htim2);
    HAL_TIM_Base_Start_IT(&htim4); //使能定时器中断 ，周期定时器 T =5ms
    HAL_TIM_Base_Start_IT(&htim5); //使能定时器中断 ，周期定时器 T =0.5ms

  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */

    stm_gpio_IOconfig();
    umain();

  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_RCC_PWR_CLK_ENABLE();
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE|RCC_OSCILLATORTYPE_LSE;
  RCC_OscInitStruct.HSEState = RCC_HSE_ON;
  RCC_OscInitStruct.LSEState = RCC_LSE_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
  RCC_OscInitStruct.PLL.PLLM = 15;
  RCC_OscInitStruct.PLL.PLLN = 216;
  RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV2;
  RCC_OscInitStruct.PLL.PLLQ = 4;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Activate the Over-Drive mode
  */
  if (HAL_PWREx_EnableOverDrive() != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV4;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV2;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_5) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

//1、B码
void  B_CODE_fun(void)
{
    if(g_Bcode_state == 1)         //上升沿中断
    {
        g_Bcode_state = 2;
        g_Bcode_temp_time = g_tim5_100us;
        B_code_GPIO_FALLING();    //启动下降沿中断
    }
    else
    if(g_Bcode_state == 2)        //下降沿中断
    {
        g_Bcode_state = 1;
        
        //计算高电平持续时间，单位100us
        if(g_Bcode_temp_time <= g_tim5_100us)
            g_Bcode_time = (g_tim5_100us - g_Bcode_temp_time);  
        else
            g_Bcode_time = (0xffffffff - g_Bcode_temp_time + g_tim5_100us );
        
        g_Bcode_flg = 1;        
        B_code_GPIO_RISING();     //启动下降沿中断
				bcode_recv(g_Bcode_time);
    }
    
}

void HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin)
{
	if(GPIO_Pin == GPIO_PIN_6)
	{
		if(1);                  //判断是否开启了B码功能
          B_CODE_fun();
                       		
	}
}

//2、TIM_CAPTURE_CALLBACK
void HAL_TIM_IC_CaptureCallback(TIM_HandleTypeDef *htim)
{
	if (htim->Instance == htim_adc.Instance)
	{
		if(htim->Channel == HAL_TIM_ACTIVE_CHANNEL_3) //fm2
	    {
	        switch(capture_Cnt)
            {
		        case 0:
                    overload_Cnt =0;  //清空溢出标志位

		        	capture_Buf[0] = HAL_TIM_ReadCapturedValue(&htim_adc,TIM_CHANNEL_3);              //得到捕获值
		            __HAL_TIM_SET_CAPTUREPOLARITY(&htim_adc, TIM_CHANNEL_3, TIM_ICPOLARITY_FALLING);  //设置下降沿捕获
		            capture_Cnt++;
		        break;

		        case 1:
		        	capture_Buf[1] = HAL_TIM_ReadCapturedValue(&htim_adc,TIM_CHANNEL_3);              //得到捕获值
		            __HAL_TIM_SET_CAPTUREPOLARITY(&htim_adc,TIM_CHANNEL_3,TIM_ICPOLARITY_RISING);     //设置上升沿捕获
		            capture_Cnt++;
		        break;

		        case 2:
		        	capture_Buf[2] = HAL_TIM_ReadCapturedValue(&htim_adc,TIM_CHANNEL_3);   //得到捕获值
		            HAL_TIM_IC_Stop_IT(&htim_adc,TIM_CHANNEL_3);                           //停止输入捕获

                    if(overload_Cnt)
                    {
                        if(capture_Buf[1] > capture_Buf[0])
                        {
                            high_time = capture_Buf[1] - capture_Buf[0] ;
                        }
                        else
                        {
                            high_time = capture_Buf[1] - capture_Buf[0] + overload_Cnt * 0xFFFF;
                        }

                        if(capture_Buf[2] > capture_Buf[1])
                        {
                            low_time = capture_Buf[2] - capture_Buf[1] ;
                        }
                        else
                        {
                            low_time = capture_Buf[2] - capture_Buf[1] + overload_Cnt * 0xFFFF;
                        }

                        HL_time = high_time *0.001;
                        LL_time = low_time  *0.001;
                        fre = 1 / (HL_time+LL_time) * 1000;
                    }
                    else
                    {
                        high_time = capture_Buf[1] - capture_Buf[0];                            //高电平时间
                        low_time  = capture_Buf[2] - capture_Buf[1] ;                          //低电平时间

                        HL_time = high_time *0.001;
                        LL_time = low_time  *0.001;
                        fre = 1 / (HL_time+LL_time) * 1000;
                    }
                    fre*=2;//TODO
//                  if ((65.0 < fre) || (fre < 35.0))
                    if ((75.0 < fre) || (fre < 35.0)) //原13606测频范围
                    {
                        fre = fre;  //err
                    } else
                        g_FM2 = fre;

		            capture_Cnt =0;   //清空标志位
		        	__HAL_TIM_SET_CAPTUREPOLARITY(&htim_adc,TIM_CHANNEL_3,TIM_INPUTCHANNELPOLARITY_RISING);
		            HAL_TIM_IC_Start_IT(&htim_adc,TIM_CHANNEL_3);
		        break;
	        }
        }

        if(htim->Channel == HAL_TIM_ACTIVE_CHANNEL_2) //fm1
        {
		    switch(capture_Cnt1)
            {
		    	case 0:
                    overload_Cnt1 =0;  //清空溢出标志位

		    	    capture_Buf[3] = HAL_TIM_ReadCapturedValue(&htim_adc,TIM_CHANNEL_2);  //得到捕获值
		            __HAL_TIM_SET_CAPTUREPOLARITY(&htim_adc,TIM_CHANNEL_2,TIM_ICPOLARITY_FALLING);  //设置下降沿捕获
		            capture_Cnt1++;
		        break;

		        case 1:
		        	capture_Buf[4] = HAL_TIM_ReadCapturedValue(&htim_adc,TIM_CHANNEL_2);  //得到捕获值
		            __HAL_TIM_SET_CAPTUREPOLARITY(&htim_adc,TIM_CHANNEL_2,TIM_ICPOLARITY_RISING);  //设置上升沿捕获
		            capture_Cnt1++;
		        break;

		        case 2:
		        	capture_Buf[5] = HAL_TIM_ReadCapturedValue(&htim_adc,TIM_CHANNEL_2);  //得到捕获值
		            HAL_TIM_IC_Stop_IT(&htim_adc, TIM_CHANNEL_2);                         //停止输入捕获

                    if(overload_Cnt1)
                    {

                        if(capture_Buf[4] > capture_Buf[3])
                        {
                            high_time1 = capture_Buf[4] - capture_Buf[3] ;
                        }
                        else
                        {
                            high_time1 = capture_Buf[4] - capture_Buf[3] + overload_Cnt * 0xFFFF;
                        }

                        if(capture_Buf[5] > capture_Buf[4])
                        {
                            low_time1 = capture_Buf[5] - capture_Buf[4] ;
                        }
                        else
                        {
                            low_time1 = capture_Buf[5] - capture_Buf[4] + overload_Cnt * 0xFFFF;
                        }

                        HL_time1 = high_time1 *0.001;
                        LL_time1 = low_time1  *0.001;
                        fre1 = 1 / (HL_time1 + LL_time1) * 1000;
                    }
                    else
                    {
                        high_time1 = capture_Buf[4] - capture_Buf[3];                            //高电平时间
                        low_time1  = capture_Buf[5] - capture_Buf[4] ;                          //低电平时间

                        HL_time1 = high_time1 *0.001;
                        LL_time1 = low_time1  *0.001;
                        fre1 = 1 / (HL_time1 + LL_time1) * 1000;
                    }
                    fre1*=2;//TODO
//                  if ((65.0 < fre1) || (fre1 < 35.0))
                    if ((99.0 < fre1) || (fre1 < 2.0)) //原13606测频范围
                    {
                        fre1 = fre1;
                    } else
                        g_FM1 = fre1;

		            capture_Cnt1 =0;  //清空标志位
		        	__HAL_TIM_SET_CAPTUREPOLARITY(&htim_adc, TIM_CHANNEL_2, TIM_INPUTCHANNELPOLARITY_RISING);
		            HAL_TIM_IC_Start_IT(&htim_adc, TIM_CHANNEL_2);
		        break;
	        }

        }
    }
}

//3、TIM_CALLBACK
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
    if (htim->Instance == htim2.Instance)
    {//TODO ADC
#ifndef DEBUG
        task_AD();        //AD采样中断
#endif
    }

    if (htim->Instance == htim_adc.Instance)  //频率, 溢出
    {
        overload_Cnt++;
        overload_Cnt1++;
        if (overload_Cnt >= TIM_ICOF_MAX)
        {
            overload_Cnt = 0;
            overload_Cnt1 = 0;
            fre = 0;
            fre1 = 0;
        }
    }

    if (htim->Instance == htim4.Instance)  //周期为5 ms的定时中断
    {
#ifndef DEBUG
        temp_cnt++;
        if (temp_cnt >= 4) {
            readAD7792Data();
            temp_cnt = 0;
        }
        task_Fun();        //保护任务中断
#endif
    }

    if (htim->Instance == htim5.Instance)  //周期为0.5ms的定时中断
    {
#ifndef DEBUG
        task_500us();
#endif
        g_tim5_100us += 5;
    }
}

/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}
#ifdef USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
